import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { ApiService } from './api.service';

interface Student {
  studentId: string;
  fullName: string;
  course: string;
  yearLevel: string;
  section: string;
  email: string;
  phoneNumber?: string;
  status: string;
}

@Injectable({
  providedIn: 'root'
})
export class StudentAuthService {
  private currentStudentSubject = new BehaviorSubject<Student | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);

  public currentStudent$ = this.currentStudentSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(private apiService: ApiService) {
    this.checkStoredAuth();
  }

  /**
   * Check if there's stored authentication data
   */
  private checkStoredAuth(): void {
    const storedStudent = localStorage.getItem('currentStudent');
    const storedToken = localStorage.getItem('studentToken');

    if (storedStudent && storedToken) {
      try {
        const student = JSON.parse(storedStudent);
        this.currentStudentSubject.next(student);
        this.isAuthenticatedSubject.next(true);
        console.log('🔄 Restored student authentication from localStorage:', student.fullName);
      } catch (error) {
        console.error('Error parsing stored student data:', error);
        this.clearStoredAuth();
      }
    }
  }

  /**
   * Force refresh authentication state from localStorage
   */
  refreshAuthState(): void {
    console.log('🔄 Forcing student authentication state refresh...');
    this.checkStoredAuth();
  }

  /**
   * Student login method
   */
  studentLogin(studentId: string, password: string): Observable<boolean> {
    console.log('🚀 Starting student login for:', studentId);
    console.log('🔍 Current auth state before login:', this.isAuthenticated());
    console.log('👤 Current student before login:', this.getCurrentStudent());

    return this.apiService.login({ studentId, password }).pipe(
      tap((response: any) => {
        console.log('🔍 Raw API Response:', response);
      }),
      map((response: any) => {
        console.log('🔍 Processing API Response:', response);

        if (response && response.success && response.data) {
          const student: Student = {
            studentId: response.data.StudentID,
            fullName: response.data.FullName,
            course: response.data.Course,
            yearLevel: response.data.YearLevel,
            section: response.data.Section,
            email: response.data.Email,
            phoneNumber: response.data.PhoneNumber,
            status: response.data.Status
          };

          console.log('✅ Student data processed:', student);

          // Store in localStorage first (this is synchronous)
          localStorage.setItem('currentStudent', JSON.stringify(student));
          localStorage.setItem('studentToken', 'student-token-' + Date.now());

          // Then update the subjects (this triggers observables)
          this.currentStudentSubject.next(student);
          this.isAuthenticatedSubject.next(true);

          console.log('✅ Student login successful, authentication state updated');
          console.log('🔐 Current auth state:', this.isAuthenticated());
          console.log('👤 Current student:', this.getCurrentStudent());
          console.log('💾 LocalStorage student:', localStorage.getItem('currentStudent'));
          console.log('💾 LocalStorage token:', localStorage.getItem('studentToken'));

          return true;
        } else {
          console.error('❌ Student login failed - invalid response structure');
          console.error('Response:', response);
          return false;
        }
      }),
      catchError((error) => {
        console.error('❌ Student login error:', error);
        return of(false);
      })
    );
  }

  /**
   * Get current student
   */
  getCurrentStudent(): Student | null {
    return this.currentStudentSubject.value;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  /**
   * Get stored token
   */
  getToken(): string | null {
    return localStorage.getItem('studentToken');
  }

  /**
   * Logout method
   */
  logout(): void {
    console.log('🚪 Student logging out...');
    this.clearStoredAuth();
    this.currentStudentSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    console.log('✅ Student logout complete');
  }

  /**
   * Clear stored authentication data
   */
  private clearStoredAuth(): void {
    localStorage.removeItem('currentStudent');
    localStorage.removeItem('studentToken');
  }

  /**
   * Validate student session (can be extended to check with backend)
   */
  validateSession(): Observable<boolean> {
    const student = this.getCurrentStudent();
    const token = this.getToken();
    
    if (!student || !token) {
      this.logout();
      return of(false);
    }

    // For now, just return true if we have stored data
    // This can be extended to validate with backend
    return of(true);
  }

  /**
   * Update student profile
   */
  updateProfile(updates: Partial<Student>): Observable<boolean> {
    const currentStudent = this.getCurrentStudent();
    if (!currentStudent) {
      return of(false);
    }

    const updatedStudent = { ...currentStudent, ...updates };
    
    // Update localStorage
    localStorage.setItem('currentStudent', JSON.stringify(updatedStudent));
    
    // Update subject
    this.currentStudentSubject.next(updatedStudent);
    
    console.log('✅ Student profile updated:', updatedStudent);
    return of(true);
  }

  /**
   * Check if student has specific permissions
   */
  hasPermission(permission: string): boolean {
    const student = this.getCurrentStudent();
    if (!student) return false;

    // Students have basic permissions by default
    const studentPermissions = [
      'view_books',
      'borrow_books',
      'view_loans',
      'renew_books',
      'make_reservations',
      'view_profile',
      'update_profile'
    ];

    return studentPermissions.includes(permission);
  }

  /**
   * Get student dashboard stats
   */
  getDashboardStats(): Observable<any> {
    // Mock data - replace with actual API call
    return of({
      totalBooksLoaned: 3,
      overdueBooks: 1,
      availableRenewals: 5,
      libraryFines: 5,
      reservations: 2,
      favoriteBooks: 12
    });
  }
}
