import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { ApiService } from './api.service';

interface Faculty {
  facultyId: string;
  fullName: string;
  department: string;
  position: string;
  email: string;
  phoneNumber?: string;
  status: string;
  specialization?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FacultyAuthService {
  private currentFacultySubject = new BehaviorSubject<Faculty | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);

  public currentFaculty$ = this.currentFacultySubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(private apiService: ApiService) {
    this.checkStoredAuth();
  }

  /**
   * Check if there's stored authentication data
   */
  private checkStoredAuth(): void {
    const storedFaculty = localStorage.getItem('currentFaculty');
    const storedToken = localStorage.getItem('facultyToken');

    if (storedFaculty && storedToken) {
      try {
        const faculty = JSON.parse(storedFaculty);
        this.currentFacultySubject.next(faculty);
        this.isAuthenticatedSubject.next(true);
        console.log('🔄 Restored faculty authentication from localStorage:', faculty.fullName);
      } catch (error) {
        console.error('Error parsing stored faculty data:', error);
        this.clearStoredAuth();
      }
    }
  }

  /**
   * Force refresh authentication state from localStorage
   */
  refreshAuthState(): void {
    console.log('🔄 Forcing faculty authentication state refresh...');
    this.checkStoredAuth();
  }

  /**
   * Faculty login method
   */
  facultyLogin(email: string, password: string): Observable<boolean> {
    console.log('🚀 Starting faculty login for:', email);
    console.log('🔍 Current auth state before login:', this.isAuthenticated());
    console.log('👤 Current faculty before login:', this.getCurrentFaculty());

    return this.apiService.facultyLogin({ email, password }).pipe(
      tap((response: any) => {
        console.log('🔍 Raw API Response:', response);
      }),
      map((response: any) => {
        console.log('🔍 Processing API Response:', response);

        if (response && response.success && response.data) {
          const faculty: Faculty = {
            facultyId: response.data.FacultyID,
            fullName: response.data.FullName,
            department: response.data.Department,
            position: response.data.Position,
            email: response.data.Email,
            phoneNumber: response.data.PhoneNumber,
            status: response.data.Status,
            specialization: response.data.Specialization
          };

          console.log('✅ Faculty data processed:', faculty);

          // Store in localStorage first (this is synchronous)
          localStorage.setItem('currentFaculty', JSON.stringify(faculty));
          localStorage.setItem('facultyToken', 'faculty-token-' + Date.now());

          // Then update the subjects (this triggers observables)
          this.currentFacultySubject.next(faculty);
          this.isAuthenticatedSubject.next(true);

          console.log('✅ Faculty login successful, authentication state updated');
          console.log('🔐 Current auth state:', this.isAuthenticated());
          console.log('👤 Current faculty:', this.getCurrentFaculty());
          console.log('💾 LocalStorage faculty:', localStorage.getItem('currentFaculty'));
          console.log('💾 LocalStorage token:', localStorage.getItem('facultyToken'));

          return true;
        } else {
          console.error('❌ Faculty login failed - invalid response structure');
          console.error('Response:', response);
          return false;
        }
      }),
      catchError((error) => {
        console.error('❌ Faculty login error:', error);
        return of(false);
      })
    );
  }

  /**
   * Get current faculty
   */
  getCurrentFaculty(): Faculty | null {
    return this.currentFacultySubject.value;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  /**
   * Get stored token
   */
  getToken(): string | null {
    return localStorage.getItem('facultyToken');
  }

  /**
   * Logout method
   */
  logout(): void {
    console.log('🚪 Faculty logging out...');
    this.clearStoredAuth();
    this.currentFacultySubject.next(null);
    this.isAuthenticatedSubject.next(false);
    console.log('✅ Faculty logout complete');
  }

  /**
   * Clear stored authentication data
   */
  private clearStoredAuth(): void {
    localStorage.removeItem('currentFaculty');
    localStorage.removeItem('facultyToken');
  }

  /**
   * Validate faculty session (can be extended to check with backend)
   */
  validateSession(): Observable<boolean> {
    const faculty = this.getCurrentFaculty();
    const token = this.getToken();
    
    if (!faculty || !token) {
      this.logout();
      return of(false);
    }

    // For now, just return true if we have stored data
    // This can be extended to validate with backend
    return of(true);
  }

  /**
   * Update faculty profile
   */
  updateProfile(updates: Partial<Faculty>): Observable<boolean> {
    const currentFaculty = this.getCurrentFaculty();
    if (!currentFaculty) {
      return of(false);
    }

    const updatedFaculty = { ...currentFaculty, ...updates };
    
    // Update localStorage
    localStorage.setItem('currentFaculty', JSON.stringify(updatedFaculty));
    
    // Update subject
    this.currentFacultySubject.next(updatedFaculty);
    
    console.log('✅ Faculty profile updated:', updatedFaculty);
    return of(true);
  }

  /**
   * Check if faculty has specific permissions
   */
  hasPermission(permission: string): boolean {
    const faculty = this.getCurrentFaculty();
    if (!faculty) return false;

    // Faculty have extended permissions
    const facultyPermissions = [
      'view_books',
      'borrow_books',
      'view_loans',
      'renew_books',
      'make_reservations',
      'view_profile',
      'update_profile',
      'manage_course_materials',
      'view_student_records',
      'create_reading_lists',
      'access_research_tools',
      'manage_class_resources'
    ];

    return facultyPermissions.includes(permission);
  }

  /**
   * Get faculty dashboard stats
   */
  getDashboardStats(): Observable<any> {
    // Mock data - replace with actual API call
    return of({
      totalBooksLoaned: 8,
      overdueBooks: 0,
      availableRenewals: 12,
      libraryFines: 0,
      reservations: 3,
      courseMaterials: 25,
      activeClasses: 4,
      researchProjects: 2
    });
  }
}
