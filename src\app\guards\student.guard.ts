import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, map } from 'rxjs';
import { StudentAuthService } from '../services/student-auth.service';

@Injectable({
  providedIn: 'root'
})
export class StudentGuard implements CanActivate {

  constructor(
    private studentAuthService: StudentAuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    console.log('🔐 StudentGuard: Checking student access...');
    console.log('🔍 Current student auth state:', this.studentAuthService.isAuthenticated());
    console.log('👤 Current student:', this.studentAuthService.getCurrentStudent());

    // Check if already authenticated
    if (this.studentAuthService.isAuthenticated()) {
      const student = this.studentAuthService.getCurrentStudent();
      console.log('👤 Current student from guard:', student);
      
      if (student) {
        console.log(`✅ Student access granted for: ${student.fullName} (${student.studentId})`);
        return true;
      } else {
        console.log('❌ No current student found despite authentication');
      }
    }

    // If not immediately authenticated, validate session
    return this.studentAuthService.validateSession().pipe(
      map((isValid: boolean) => {
        console.log('✅ Student session validation result:', isValid);

        if (isValid && this.studentAuthService.isAuthenticated()) {
          const student = this.studentAuthService.getCurrentStudent();
          console.log('👤 Current student from guard after validation:', student);

          if (student) {
            console.log(`✅ Student access granted after validation for: ${student.fullName} (${student.studentId})`);
            return true;
          } else {
            console.log('❌ No current student found after validation');
          }
        } else {
          console.log('❌ Student session invalid or not authenticated');
        }

        console.log('❌ Student access denied - redirecting to login');
        this.router.navigate(['/login'], {
          queryParams: { returnUrl: state.url }
        });
        return false;
      })
    );
  }
}
